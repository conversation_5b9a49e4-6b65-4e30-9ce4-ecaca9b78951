#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试金刚狼处理器
"""

import os
import sys
from pathlib import Path

def test_wolverine_processor():
    """测试金刚狼处理器"""
    print("=== 金刚狼处理器测试 ===")
    
    # 测试处理器导入
    try:
        from Codebase_Wolverine import WolverineProcessor
        print("✅ 金刚狼处理器导入成功")
    except ImportError as e:
        print(f"❌ 金刚狼处理器导入失败: {e}")
        return False
    
    # 测试处理器类结构
    assert hasattr(WolverineProcessor, 'progress_updated'), "进度信号不存在"
    assert hasattr(WolverineProcessor, 'process_finished'), "完成信号不存在"
    print("✅ 处理器信号完整")
    
    # 测试处理器方法
    required_methods = [
        'run', 'process_video_pair', 'make_mask', 'resize_and_crop',
        'cancel', 'cleanup_temp_files'
    ]
    
    for method in required_methods:
        assert hasattr(WolverineProcessor, method), f"方法 {method} 不存在"
    print("✅ 处理器方法完整")
    
    # 测试处理器初始化
    try:
        processor = WolverineProcessor(
            "ffmpeg", ["test.mp4"], ["test_b.mp4"], "output", "config", False
        )
        print("✅ 处理器初始化成功")
        
        # 测试配置参数
        assert processor.FEATHER == 10, "FEATHER配置错误"
        assert processor.FPS == 30, "FPS配置错误"
        assert processor.SIZE_BG == 1080, "SIZE_BG配置错误"
        assert processor.SIZE_OUT == (1080, 1920), "SIZE_OUT配置错误"
        print("✅ 处理器配置正确")
        
    except Exception as e:
        print(f"❌ 处理器初始化失败: {e}")
        return False
    
    print("🎉 金刚狼处理器测试通过")
    return True

def test_moneycomehere_integration():
    """测试MoneyComehere_v2集成"""
    print("\n=== MoneyComehere_v2集成测试 ===")
    
    try:
        # 检查源代码中的导入语句
        with open("MoneyComehere_v2.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键集成点
        checks = [
            # 1. 导入语句
            ("金刚狼处理器导入", "from Codebase_Wolverine import WolverineProcessor" in content),
            
            # 2. 功能选项
            ("金刚狼功能选项", '"金刚狼（独家视频号）🦾稳过爆单", "wolverine_sph"' in content),
            
            # 3. 功能码
            ("金刚狼功能码", '"wolverine_sph"' in content and 'wolverine_sph' in content),
            
            # 4. 处理器创建
            ("金刚狼处理器创建", 'WolverineProcessor(' in content),
            
            # 5. 无验证处理
            ("无验证处理", 'start_wolverine_processing' in content),
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_passed = False
        
        if all_passed:
            print("🎉 MoneyComehere_v2集成测试通过")
        else:
            print("❌ MoneyComehere_v2集成测试失败")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_wolverine_processor()
    success2 = test_moneycomehere_integration()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！金刚狼处理器已成功集成")
    else:
        print("\n❌ 测试失败，请检查问题")
        sys.exit(1)
