[2025-07-28T15:40:21.839509 4992 ] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T15:40:21.839591 4992 ] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T15:40:21.839605 4992 ] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T15:40:21.839605 4992 ] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) base_dir = 
[2025-07-28T15:40:21.839605 4992 ] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T15:40:21.839605 4992 ] Config: (default) compiler = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) compiler_check = mtime
[2025-07-28T15:40:21.839605 4992 ] Config: (default) compiler_type = auto
[2025-07-28T15:40:21.839605 4992 ] Config: (default) compression = true
[2025-07-28T15:40:21.839605 4992 ] Config: (default) compression_level = 0
[2025-07-28T15:40:21.839605 4992 ] Config: (default) cpp_extension = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) debug = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) debug_dir = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) debug_level = 2
[2025-07-28T15:40:21.839605 4992 ] Config: (default) depend_mode = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) direct_mode = true
[2025-07-28T15:40:21.839605 4992 ] Config: (default) disable = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) extra_files_to_hash = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) file_clone = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) hard_link = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) hash_dir = true
[2025-07-28T15:40:21.839605 4992 ] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) ignore_options = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) inode_cache = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) keep_comments_cpp = false
[2025-07-28T15:40:21.839605 4992 ] Config: (environment) log_file = E:\MONEYC~1\bin\111~1.ONE\ccache-27280.txt
[2025-07-28T15:40:21.839605 4992 ] Config: (default) max_files = 0
[2025-07-28T15:40:21.839605 4992 ] Config: (default) max_size = 5.0 GiB
[2025-07-28T15:40:21.839605 4992 ] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T15:40:21.839605 4992 ] Config: (default) namespace = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) path = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) pch_external_checksum = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) prefix_command = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) prefix_command_cpp = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) read_only = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) read_only_direct = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) recache = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) remote_only = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) remote_storage = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) reshare = false
[2025-07-28T15:40:21.839605 4992 ] Config: (default) run_second_cpp = true
[2025-07-28T15:40:21.839605 4992 ] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T15:40:21.839605 4992 ] Config: (default) stats = true
[2025-07-28T15:40:21.839605 4992 ] Config: (default) stats_log = 
[2025-07-28T15:40:21.839605 4992 ] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T15:40:21.839605 4992 ] Config: (default) umask = 
[2025-07-28T15:40:21.839670 4992 ] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T15:40:21.842129 4992 ] Hostname: DESKTOP-419NV80
[2025-07-28T15:40:21.842145 4992 ] Working directory: E:\MONEYC~1\bin\111~1.ONE
[2025-07-28T15:40:21.842162 4992 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T15:40:21.842166 4992 ] Compiler type: gcc
[2025-07-28T15:40:21.842333 4992 ] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T15:40:21.842416 4992 ] Source file: static_src\OnefileBootstrap.c
[2025-07-28T15:40:21.842420 4992 ] Object file: static_src\OnefileBootstrap.o
[2025-07-28T15:40:21.842520 4992 ] Trying direct lookup
[2025-07-28T15:40:21.842653 4992 ] Manifest key: cf78u7ac640g5e2sjjlikan883bhehqli
[2025-07-28T15:40:21.842869 4992 ] No cf78u7ac640g5e2sjjlikan883bhehqli in local storage
[2025-07-28T15:40:21.843180 4992 ] Running preprocessor
[2025-07-28T15:40:21.843281 4992 ] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd -E -o C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\ccache/tmp/cpp_stdout.tmp.kzeFtq.i static_src\\OnefileBootstrap.c
[2025-07-28T15:40:22.697730 4992 ] Got result key from preprocessor
[2025-07-28T15:40:22.697751 4992 ] Result key: 432bi3l5quknbpsvrpsmct45789u3iv8i
[2025-07-28T15:40:22.698477 4992 ] Retrieved 432bi3l5quknbpsvrpsmct45789u3iv8i from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/3/2bi3l5quknbpsvrpsmct45789u3iv8iR)
[2025-07-28T15:40:22.698949 4992 ] Reading embedded entry #0 .o (614331 bytes)
[2025-07-28T15:40:22.698959 4992 ] Writing to static_src\OnefileBootstrap.o
[2025-07-28T15:40:22.699271 4992 ] Succeeded getting cached result
[2025-07-28T15:40:22.704984 4992 ] Added result key to manifest cf78u7ac640g5e2sjjlikan883bhehqli
[2025-07-28T15:40:22.705012 4992 ] Using Zstandard with default compression level 1
[2025-07-28T15:40:22.705792 4992 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_cf.lock
[2025-07-28T15:40:22.705948 4992 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_cf.lock
[2025-07-28T15:40:22.706123 4992 ] Stored cf78u7ac640g5e2sjjlikan883bhehqli in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/f/78u7ac640g5e2sjjlikan883bhehqliM)
[2025-07-28T15:40:22.706166 4992 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T15:40:22.706247 4992 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T15:40:22.706532 4992 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T15:40:22.706582 4992 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T15:40:22.706587 4992 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_cf.lock
[2025-07-28T15:40:22.706616 4992 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_cf.lock
[2025-07-28T15:40:22.706675 4992 ] Result: direct_cache_miss
[2025-07-28T15:40:22.706679 4992 ] Result: local_storage_hit
[2025-07-28T15:40:22.706681 4992 ] Result: local_storage_read_hit
[2025-07-28T15:40:22.706684 4992 ] Result: local_storage_read_miss
[2025-07-28T15:40:22.706687 4992 ] Result: local_storage_write
[2025-07-28T15:40:22.706689 4992 ] Result: preprocessed_cache_hit
[2025-07-28T15:40:22.706697 4992 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-07-28T15:40:22.706772 4992 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-07-28T15:40:22.707008 4992 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-07-28T15:40:22.707043 4992 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-07-28T15:40:22.707058 4992 ] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T15:40:22.707134 4992 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T15:40:22.707455 4992 ] No automatic cleanup needed (size 164.4 MiB, files 2874, max size 5.0 GiB)
[2025-07-28T15:40:22.707461 4992 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T15:40:22.707500 4992 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T15:40:22.707593 4992 ] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.5fRvAS.tmp
[2025-07-28T15:40:22.708212 4992 ] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.kzeFtq.i
