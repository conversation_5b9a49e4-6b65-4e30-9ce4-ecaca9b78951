#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JF叠加处理器 ‑ 修正版
=====================
对照用户实际运行日志，修正了以下问题：
1. **FFmpeg 路径重复 bin/bin**：`_find_bin()` 现会依次查找脚本同级目录、上级 `bin` 目录以及同级 `bin` 目录，避免因打包时目录结构变化导致的找不到可执行文件或出现 `bin/bin` 嵌套路径。
2. **boxblur 表达式解析错误**：简化羽化滤镜，改用确定半径 `12`，公式 `boxblur=luma_radius=12:luma_power=1`，避免 FFmpeg 表达式解析失败 (`Invalid argument`).
3. **输出兼容性**：在导出的 H.264 命令中加入 `-pix_fmt yuv420p` 保证播放器兼容；在最终 `-aspect` 步骤确保只复制流。
4. **更友好的错误提示**：捕获 `subprocess.CalledProcessError` 时弹窗显示 FFmpeg 退出码及失败的命令，便于快速定位。

依赖：PyQt5 + FFmpeg ≥ 6.0（脚本自带检查）
"""
import os
import sys
import json
import shutil
import subprocess
import tempfile
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication,
    QWidget,
    QLabel,
    QLineEdit,
    QPushButton,
    QFileDialog,
    QVBoxLayout,
    QHBoxLayout,
    QProgressBar,
    QMessageBox,
)


class VideoProcessor(QWidget):
    STEPS = 6

    def __init__(self):
        super().__init__()
        self.setWindowTitle("JF 叠加处理器 (修正版)")
        self.resize(560, 240)

        # 定位 ffmpeg / ffprobe
        self.base_dir = Path(sys.argv[0]).resolve().parent
        self.ffmpeg = self._find_bin("ffmpeg.exe")
        self.ffprobe = self._find_bin("ffprobe.exe")

        # UI
        self._init_ui()
        self.temp_dir: Path | None = None

    # ---------- GUI ---------- #
    def _init_ui(self):
        self.a_edit = QLineEdit()
        self.b_edit = QLineEdit()
        btn_a = QPushButton("选择 A 视频")
        btn_b = QPushButton("选择 B 视频")
        btn_run = QPushButton("开始处理")
        self.progress = QProgressBar()
        self.progress.setMaximum(self.STEPS)

        btn_a.clicked.connect(self._choose_a)
        btn_b.clicked.connect(self._choose_b)
        btn_run.clicked.connect(self._run)

        lay = QVBoxLayout(self)
        for lbl, edit, btn in (("A 视频:", self.a_edit, btn_a), ("B 视频:", self.b_edit, btn_b)):
            line = QHBoxLayout()
            line.addWidget(QLabel(lbl))
            line.addWidget(edit)
            line.addWidget(btn)
            lay.addLayout(line)
        lay.addWidget(btn_run)
        lay.addWidget(self.progress)

    # ---------- Helpers ---------- #
    def _find_bin(self, exe: str) -> str:
        """在若干常见位置查找 ffmpeg/ffprobe 可执行文件"""
        candidates = [
            self.base_dir / exe,                       # dist/bin/ffmpeg.exe
            self.base_dir / "bin" / exe,              # dist/bin/bin/ffmpeg.exe (旧版本)
            self.base_dir.parent / "bin" / exe,       # dist/bin/../bin/ffmpeg.exe (打包上层)
        ]
        for p in candidates:
            if p.exists():
                return str(p)
        QMessageBox.critical(self, "错误", f"未找到 {exe}，请确认 bin 目录结构！")
        sys.exit(1)

    def _choose_a(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择 A 视频", "", "视频文件 (*.mp4 *.mov *.mkv *.avi)")
        if file:
            self.a_edit.setText(file)

    def _choose_b(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择 B 视频", "", "视频文件 (*.mp4 *.mov *.mkv *.avi)")
        if file:
            self.b_edit.setText(file)

    def _run(self):
        a_path = Path(self.a_edit.text())
        b_path = Path(self.b_edit.text())
        if not a_path.exists() or not b_path.exists():
            QMessageBox.warning(self, "提示", "请先选择有效的 A 与 B 视频！")
            return
        self.progress.setValue(0)
        self.temp_dir = Path(tempfile.mkdtemp(prefix="jf_", dir=a_path.parent))
        try:
            self._process(a_path, b_path)
            QMessageBox.information(self, "完成", "全部处理完成！")
        except subprocess.CalledProcessError as e:
            QMessageBox.critical(self, "FFmpeg 运行失败", f"命令退出码 {e.returncode}:\n{e.cmd}")
        except Exception as e:
            QMessageBox.critical(self, "错误", str(e))
        finally:
            if self.temp_dir:
                shutil.rmtree(self.temp_dir, ignore_errors=True)

    # ---------- Core ---------- #
    def _run_cmd(self, cmd: list[str]):
        print("RUN:", " ".join(cmd))
        subprocess.run(cmd, check=True)

    def _probe_duration(self, path: Path) -> float:
        cmd = [
            self.ffprobe,
            "-v",
            "error",
            "-select_streams",
            "v:0",
            "-show_entries",
            "format=duration",
            "-of",
            "json",
            str(path),
        ]
        out = subprocess.check_output(cmd, text=True)
        return float(json.loads(out)["format"]["duration"])

    def _update(self, step: int):
        self.progress.setValue(step)
        QApplication.processEvents()

    def _process(self, a: Path, b: Path):
        # 1. 提取 A 音频
        audio_file = self.temp_dir / "a_audio.m4a"
        self._run_cmd([
            self.ffmpeg, "-y", "-i", str(a), "-vn", "-acodec", "copy", str(audio_file)
        ])
        self._update(1)

        # 2. B 调整为 1:1 并循环到 A 时长
        duration = self._probe_duration(a)
        b_sq = self.temp_dir / "b_square.mp4"
        self._run_cmd([
            self.ffmpeg, "-y",
            "-stream_loop", "-1", "-i", str(b),
            "-t", f"{duration}",
            "-vf", "scale=1080:1080:force_original_aspect_ratio=decrease,pad=1080:1080:(ow-iw)/2:(oh-ih)/2,setsar=1",
            "-r", "30", "-c:v", "libx264", "-preset", "fast", "-crf", "23", "-an",
            str(b_sq)
        ])
        self._update(2)

        # 3. 叠加 A 于 B (羽化 12px)
        ab_overlay = self.temp_dir / "ab_overlay.mp4"
        overlay_filter = (
            "[1:v]scale=-2:1080,boxblur=luma_radius=12:luma_power=1[fg];"
            "[0:v][fg]overlay=(W-w)/2:(H-h)/2:format=auto"
        )
        self._run_cmd([
            self.ffmpeg, "-y", "-i", str(b_sq), "-i", str(a),
            "-filter_complex", overlay_filter,
            "-c:v", "libx264", "-preset", "fast", "-crf", "18", "-shortest", "-an",
            str(ab_overlay)
        ])
        self._update(3)

        # 4. 改为 9:16 拉伸铺满 + A 音频
        ab_916 = self.temp_dir / "ab_916.mp4"
        self._run_cmd([
            self.ffmpeg, "-y", "-i", str(ab_overlay), "-i", str(audio_file),
            "-filter_complex", "[0:v]scale=1080:1920,setsar=1[v]",
            "-map", "[v]", "-map", "1:a:0",
            "-c:v", "libx264", "-preset", "fast", "-crf", "18", "-pix_fmt", "yuv420p",
            "-c:a", "aac", "-b:a", "192k", "-r", "30",
            str(ab_916)
        ])
        self._update(4)

        # 5. 保存 tempa.mp4
        tempa = self.temp_dir / "tempa.mp4"
        shutil.copy(ab_916, tempa)
        self._update(5)

        # 6. 设置显示比例 1:1 并输出最终文件
        final_path = a.parent / f"{a.stem}_JFsph.mp4"
        self._run_cmd([
            self.ffmpeg, "-y", "-i", str(tempa), "-aspect", "1:1", "-c", "copy", str(final_path)
        ])
        self._update(6)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = VideoProcessor()
    win.show()
    sys.exit(app.exec_())
