#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A+B 竖屏合成（OpenCV 处理画面，FFmpeg 双 DAR 修正并保留音频）
"""

import cv2
import numpy as np
import subprocess
import sys
from pathlib import Path

# ========= 基本配置 =========
FEATHER = 10
FPS     = 30
SIZE_BG = 1080
SIZE_OUT = (1080, 1920)

root   = Path(__file__).resolve().parent
ffmpeg = root / "ffmpeg.exe"
file_a = root / "A.mp4"
file_b = root / "B.mp4"
temp   = root / "temp.mp4"
out_mp4= root / "output.mp4"

# ========= 检查文件 =========
for f in (ffmpeg, file_a, file_b):
    if not f.exists():
        sys.exit(f"缺少文件: {f.name}")

# ========= 辅助函数 =========
def make_mask(h, w, feather=10):
    mask = np.ones((h, w), np.uint8) * 255
    mask[:feather, :] = mask[-feather:, :] = 0
    mask[:, :feather] = mask[:, -feather:] = 0
    return cv2.GaussianBlur(mask, (feather*2+1, feather*2+1), 0)

def resize_and_crop(frame, target):
    h, w = frame.shape[:2]
    scale = target / min(h, w)
    new_w, new_h = int(w * scale), int(h * scale)
    frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    x0, y0 = (new_w - target)//2, (new_h - target)//2
    return frame[y0:y0+target, x0:x0+target]

# ========= 获取 A 时长/帧数 =========
cap_a = cv2.VideoCapture(str(file_a))
fps_a = cap_a.get(cv2.CAP_PROP_FPS) or FPS
total_frames = int(cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
frames_out = int((total_frames / fps_a) * FPS)
cap_b = cv2.VideoCapture(str(file_b))

# ========= 写 OpenCV 合成视频 =========
fourcc = cv2.VideoWriter_fourcc(*'mp4v')
writer = cv2.VideoWriter(str(temp), fourcc, FPS, SIZE_OUT)
if not writer.isOpened():
    sys.exit("VideoWriter 无法打开！")

print(f"► 开始 OpenCV 合成 {frames_out} 帧…")
mask_cache = {}
frame_idx = 0

while frame_idx < frames_out:
    ret_b, frame_b = cap_b.read()
    if not ret_b:
        cap_b.set(cv2.CAP_PROP_POS_FRAMES, 0)
        continue
    frame_b = resize_and_crop(frame_b, SIZE_BG)

    ret_a, frame_a = cap_a.read()
    if not ret_a: break
    h_a, w_a = frame_a.shape[:2]
    scale = SIZE_BG / h_a
    new_w = int(w_a * scale)
    frame_a = cv2.resize(frame_a, (new_w, SIZE_BG), interpolation=cv2.INTER_LINEAR)

    if new_w not in mask_cache:
        mask_cache[new_w] = make_mask(SIZE_BG, new_w, FEATHER)
    mask = mask_cache[new_w]
    mask_f = mask / 255.0
    inv = 1 - mask_f

    x = (SIZE_BG - new_w)//2
    roi = frame_b[:, x:x+new_w]
    blended = (roi * inv[...,None] + frame_a * mask_f[...,None]).astype(np.uint8)
    frame_b[:, x:x+new_w] = blended

    stretched = cv2.resize(frame_b, SIZE_OUT, interpolation=cv2.INTER_LINEAR)
    writer.write(stretched)

    if frame_idx % 100 == 0:
        print(f"  已处理 {frame_idx}/{frames_out} 帧")
    frame_idx += 1

cap_a.release(); cap_b.release(); writer.release()
print("✔ temp.mp4 画面合成完成")

# ========= FFmpeg 混流音频 + DAR =========
cmd_mix = [
    str(ffmpeg), "-y",
    "-i", str(temp),
    "-i", str(file_a),
    "-map", "0:v", "-map", "1:a?",
    "-c:v", "copy", "-c:a", "aac",
    "-aspect", "1:1",
    str(out_mp4)
]
print("► 调用 FFmpeg 混流音轨…")
subprocess.run(cmd_mix, check=True)
print("✔ output.mp4 (含音频) 生成完成 ✅")

# ========= 再执行一次极简 DAR 修正（用临时文件） =========
out_dar = root / "output_dar.mp4"
cmd_dar = [
    str(ffmpeg), "-y",
    "-i", str(out_mp4),
    "-aspect", "1:1",
    str(out_dar)
]
print("► 再次执行 DAR 修正命令（写临时文件）…")
subprocess.run(cmd_dar, check=True)

# 替换原 output.mp4
out_mp4.unlink()
out_dar.rename(out_mp4)
print("✔ 最终 DAR 修正完成 → output.mp4 ✅")


# ========= 删除中间文件 =========
try:
    temp.unlink()
except Exception:
    pass
print("🎉 全流程结束")
