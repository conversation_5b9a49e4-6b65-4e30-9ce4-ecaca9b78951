#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试金刚狼处理器功能
"""

import os
import cv2
import numpy as np
from pathlib import Path

def create_test_video(output_path, width=640, height=480, fps=30, duration=3):
    """创建测试视频"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = fps * duration
    for i in range(total_frames):
        # 创建彩色帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        # 添加渐变色
        frame[:, :, 0] = (i * 255 // total_frames) % 256  # 蓝色通道
        frame[:, :, 1] = ((i * 2) * 255 // total_frames) % 256  # 绿色通道
        frame[:, :, 2] = ((i * 3) * 255 // total_frames) % 256  # 红色通道
        
        # 添加文字
        cv2.putText(frame, f"Frame {i}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        writer.write(frame)
    
    writer.release()
    print(f"创建测试视频: {output_path}")

def test_wolverine_functionality():
    """测试金刚狼处理器功能"""
    print("=== 金刚狼处理器功能测试 ===")
    
    # 创建测试目录
    test_dir = Path("test_wolverine")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试视频
    a_video = str(test_dir / "test_a.mp4")
    b_video = str(test_dir / "test_b.mp4")
    output_dir = str(test_dir / "output")
    Path(output_dir).mkdir(exist_ok=True)
    
    # 创建A视频（竖屏）
    create_test_video(a_video, width=480, height=640, fps=30, duration=2)
    
    # 创建B视频（方形）
    create_test_video(b_video, width=640, height=640, fps=30, duration=5)
    
    # 测试处理器辅助方法
    try:
        from Codebase_Wolverine import WolverineProcessor
        
        processor = WolverineProcessor(
            "ffmpeg", [a_video], [b_video], output_dir, "test_config", False
        )
        
        # 测试make_mask方法
        mask = processor.make_mask(100, 80, 5)
        assert mask.shape == (100, 80), "mask形状错误"
        print("✅ make_mask方法测试通过")
        
        # 测试resize_and_crop方法
        test_frame = np.zeros((200, 300, 3), dtype=np.uint8)
        cropped = processor.resize_and_crop(test_frame, 150)
        assert cropped.shape == (150, 150, 3), "resize_and_crop形状错误"
        print("✅ resize_and_crop方法测试通过")
        
        print("🎉 金刚狼处理器功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print("✅ 测试文件清理完成")
        except Exception as e:
            print(f"⚠️ 清理测试文件失败: {e}")

def test_output_filename_format():
    """测试输出文件名格式"""
    print("\n=== 输出文件名格式测试 ===")
    
    try:
        from Codebase_Wolverine import WolverineProcessor
        from pathlib import Path
        
        # 测试文件名生成逻辑
        test_a_video = "/path/to/测试视频.mp4"
        a_name = Path(test_a_video).stem
        output_filename = f"{a_name}_疾风sph.mp4"
        
        expected = "测试视频_疾风sph.mp4"
        assert output_filename == expected, f"文件名格式错误: {output_filename} != {expected}"
        
        print(f"✅ 输出文件名格式正确: {output_filename}")
        return True
        
    except Exception as e:
        print(f"❌ 文件名格式测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_wolverine_functionality()
    success2 = test_output_filename_format()
    
    if success1 and success2:
        print("\n🎉 所有功能测试通过！")
    else:
        print("\n❌ 功能测试失败")
