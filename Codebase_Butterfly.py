#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import tempfile
import shutil
import cv2
import numpy as np
from PyQt5.QtCore import QThread, pyqtSignal
from datetime import datetime

class ButterflyProcessor(QThread):
    """蝴蝶翻飞-视频号处理器 - 全新六步处理流程"""
    
    progress_updated = pyqtSignal(int, str)  # 与主界面兼容的进度信号
    process_finished = pyqtSignal(bool, str)  # 与主界面兼容的完成信号
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        
        # 进度显示相关
        self.current_video_index = 0
        self.total_videos = 0
        self.used_b_videos = set()  # 记录已使用的B视频
        
        # 创建唯一的临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="butterfly_")
        print(f"[蝴蝶翻飞] 创建临时目录: {self.temp_dir}")
        
    def get_video_duration(self, video_path):
        """获取视频时长"""
        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-show_entries", "format=duration",
                 "-of", "default=noprint_wrappers=1:nokey=1", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return float(result.stdout.strip())
            return 0
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 获取视频时长失败: {e}")
            return 0
    
    def get_video_resolution(self, video_path):
        """获取视频分辨率"""
        try:
            ffprobe_path = self.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
            result = subprocess.run(
                [ffprobe_path, "-v", "quiet", "-select_streams", "v:0",
                 "-show_entries", "stream=width,height", "-of", "csv=s=x:p=0", video_path],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                resolution = result.stdout.strip()
                if 'x' in resolution:
                    width, height = resolution.split('x')
                    return int(width), int(height)
            
            # 默认分辨率
            return 720, 1280
            
        except Exception as e:
            print(f"[蝴蝶翻飞] 获取视频分辨率失败: {e}")
            return 720, 1280
    
    def run_ffmpeg_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            print(f"[蝴蝶翻飞] {step_name} 命令: {' '.join(cmd)}")
            
            self.current_process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding='utf-8', errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            stdout, stderr = self.current_process.communicate()
            
            if self.current_process.returncode != 0:
                print(f"[蝴蝶翻飞] {step_name} 失败: {stderr}")
                return False
            
            print(f"[蝴蝶翻飞] {step_name} 成功完成")
            return True
            
        except Exception as e:
            print(f"[蝴蝶翻飞] {step_name} 异常: {e}")
            return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"[蝴蝶翻飞] 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"[蝴蝶翻飞] 清理临时目录失败: {e}")
    
    def delete_used_b_videos(self):
        """删除已使用的B视频素材"""
        deleted_count = 0
        failed_count = 0
        
        for b_video_path in self.used_b_videos:
            try:
                if os.path.exists(b_video_path):
                    os.remove(b_video_path)
                    deleted_count += 1
                    print(f"[蝴蝶翻飞] 已删除B视频素材: {b_video_path}")
                else:
                    print(f"[蝴蝶翻飞] B视频素材不存在，跳过: {b_video_path}")
            except Exception as e:
                failed_count += 1
                print(f"[蝴蝶翻飞] 删除B视频素材失败: {b_video_path}, 错误: {e}")
        
        print(f"[蝴蝶翻飞] B视频素材删除完成: 成功删除 {deleted_count} 个，失败 {failed_count} 个")
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process and self.current_process.poll() is None:
            try:
                self.current_process.terminate()
            except:
                pass
    
    def run(self):
        """主处理流程"""
        try:
            print("[蝴蝶翻飞] 开始处理")
            
            # 检查输入
            if not self.a_video_list:
                self.process_finished.emit(False, "没有A视频文件")
                return
            
            if not self.b_video_list:
                self.process_finished.emit(False, "没有B视频文件")
                return
            
            self.total_videos = len(self.a_video_list)
            print(f"[蝴蝶翻飞] 总共需要处理 {self.total_videos} 个A视频")
            print(f"[蝴蝶翻飞] B视频素材数量: {len(self.b_video_list)} 个")
            
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)
            
            # 处理每个A视频
            success_count = 0
            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break
                
                self.current_video_index = i + 1
                
                # 选择B视频（循环使用）
                b_video = self.b_video_list[i % len(self.b_video_list)]
                
                if self.process_video_pair(a_video, b_video, i):
                    success_count += 1
                else:
                    print(f"[蝴蝶翻飞] 第 {i + 1} 个视频处理失败")
            
            # 删除已使用的B视频素材
            if self.delete_used_b and self.used_b_videos:
                self.delete_used_b_videos()
            
            # 完成处理
            if self.is_cancelled:
                self.process_finished.emit(False, "处理已取消")
            elif success_count > 0:
                message = f"蝴蝶翻飞处理完成！成功处理 {success_count}/{self.total_videos} 个视频"
                if self.delete_used_b and self.used_b_videos:
                    message += f"，已删除 {len(self.used_b_videos)} 个已用B视频素材"
                self.process_finished.emit(True, message)
            else:
                self.process_finished.emit(False, "所有视频处理失败")
                
        except Exception as e:
            print(f"[蝴蝶翻飞] 处理异常: {e}")
            self.process_finished.emit(False, f"处理过程中发生错误: {str(e)}")
        finally:
            self.cleanup_temp_files()

    def process_video_pair(self, a_video, b_video, pair_index):
        """处理单个视频对"""
        try:
            a_video_path = a_video['path']
            b_video_path = b_video['path']
            a_filename = os.path.splitext(a_video['filename'])[0]
            b_filename = os.path.splitext(b_video['filename'])[0]

            print(f"[蝴蝶翻飞] 开始处理第 {pair_index + 1} 个视频对: {a_filename} + {b_filename}")

            # 获取A视频时长和分辨率
            duration = self.get_video_duration(a_video_path)
            if duration <= 0:
                print(f"[蝴蝶翻飞] 无法获取A视频时长: {a_video_path}")
                return False

            width, height = self.get_video_resolution(a_video_path)
            print(f"[蝴蝶翻飞] A视频时长: {duration:.2f}秒, 分辨率: {width}x{height}")

            # 输出文件路径
            output_filename = f"{a_filename}_JFsph.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 更新进度
            base_progress = int((pair_index / self.total_videos) * 100)
            self.progress_updated.emit(base_progress, f"处理 {a_filename} + {b_filename} ({pair_index + 1}/{self.total_videos})")

            # 执行六步处理流程
            if not self.execute_butterfly_process(a_video_path, b_video_path, final_output, duration, width, height, pair_index):
                return False

            # 记录已使用的B视频
            if self.delete_used_b:
                self.used_b_videos.add(b_video_path)

            print(f"[蝴蝶翻飞] 成功处理: {output_filename}")
            return True

        except Exception as e:
            print(f"[蝴蝶翻飞] 处理视频对失败: {e}")
            return False

    def execute_butterfly_process(self, a_video_path, b_video_path, output_path, duration, width, height, pair_index):
        """执行蝴蝶翻飞六步处理流程"""
        try:
            # 计算基础进度
            base_progress = int((pair_index / self.total_videos) * 100)

            # 临时文件路径
            audio_file = os.path.join(self.temp_dir, f"audio_{pair_index}.m4a")
            b_1_1_file = os.path.join(self.temp_dir, f"b_1_1_{pair_index}.mp4")
            b_looped_file = os.path.join(self.temp_dir, f"b_looped_{pair_index}.mp4")
            overlay_file = os.path.join(self.temp_dir, f"overlay_{pair_index}.mp4")
            stretched_file = os.path.join(self.temp_dir, f"stretched_{pair_index}.mp4")
            tempa_file = os.path.join(self.temp_dir, f"tempa_{pair_index}.mp4")

            # 步骤1：提取A视频的音频
            self.progress_updated.emit(base_progress + 5, f"步骤1: 提取音频 ({pair_index + 1}/{self.total_videos})")
            if not self.step1_extract_audio(a_video_path, audio_file):
                return False

            # 步骤2：将B视频调整为1:1比例并循环到A视频时长
            self.progress_updated.emit(base_progress + 20, f"步骤2: 处理B视频1:1比例 ({pair_index + 1}/{self.total_videos})")
            if not self.step2_process_b_video(b_video_path, b_1_1_file, b_looped_file, duration):
                return False

            # 步骤3：将A视频叠加到1:1的B视频上（边缘羽化）
            self.progress_updated.emit(base_progress + 40, f"步骤3: A视频叠加B视频 ({pair_index + 1}/{self.total_videos})")
            if not self.step3_overlay_videos(a_video_path, b_looped_file, overlay_file, width, height):
                return False

            # 步骤4：将处理后的视频变更为9:16并拉伸铺满
            self.progress_updated.emit(base_progress + 60, f"步骤4: 9:16拉伸处理 ({pair_index + 1}/{self.total_videos})")
            if not self.step4_stretch_to_9_16(overlay_file, stretched_file):
                return False

            # 步骤5：导出为tempa.mp4，1080P，H.264编码，30fps
            self.progress_updated.emit(base_progress + 75, f"步骤5: 导出tempa.mp4 ({pair_index + 1}/{self.total_videos})")
            if not self.step5_export_tempa(stretched_file, audio_file, tempa_file):
                return False

            # 步骤6：修正宽高比
            self.progress_updated.emit(base_progress + 90, f"步骤6: 修正宽高比 ({pair_index + 1}/{self.total_videos})")
            if not self.step6_fix_aspect_ratio(tempa_file, output_path):
                return False

            return True

        except Exception as e:
            print(f"[蝴蝶翻飞] 执行处理流程失败: {e}")
            return False

    def step1_extract_audio(self, a_video_path, audio_file):
        """步骤1：提取A视频的音频"""
        try:
            cmd = [
                self.ffmpeg_path, "-y",
                "-i", a_video_path,
                "-vn", "-acodec", "aac",
                "-b:a", "128k",
                audio_file
            ]
            return self.run_ffmpeg_command(cmd, "提取音频")
        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤1失败: {e}")
            return False

    def step2_process_b_video(self, b_video_path, b_1_1_file, b_looped_file, duration):
        """步骤2：将B视频调整为1:1比例并循环到A视频时长"""
        try:
            # 先将B视频调整为1:1比例
            cmd1 = [
                self.ffmpeg_path, "-y",
                "-i", b_video_path,
                "-vf", "scale=1080:1080:force_original_aspect_ratio=decrease,pad=1080:1080:(ow-iw)/2:(oh-ih)/2",
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-an",  # 去除音频
                b_1_1_file
            ]

            if not self.run_ffmpeg_command(cmd1, "B视频1:1调整"):
                return False

            # 然后循环到A视频时长
            cmd2 = [
                self.ffmpeg_path, "-y",
                "-stream_loop", "-1",
                "-i", b_1_1_file,
                "-t", str(duration),
                "-c", "copy",
                b_looped_file
            ]

            return self.run_ffmpeg_command(cmd2, "B视频循环")

        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤2失败: {e}")
            return False

    def step3_overlay_videos(self, a_video_path, b_looped_file, overlay_file, width, height):
        """步骤3：将A视频叠加到1:1的B视频上（边缘羽化值12/100）"""
        try:
            # 计算A视频在1:1画面中的位置和大小
            # 保持A视频不被拉伸，最大化完整呈现
            if width > height:
                # 横向视频，以高度为准
                new_height = 1080
                new_width = int((width / height) * new_height)
                x_offset = (1080 - new_width) // 2
                y_offset = 0
            else:
                # 纵向或方形视频，以宽度为准
                new_width = 1080
                new_height = int((height / width) * new_width)
                x_offset = 0
                y_offset = (1080 - new_height) // 2

            # 羽化值12/100 = 0.12，实现边缘羽化效果
            # 使用ffmpeg的复杂滤镜实现A视频边缘羽化叠加到B视频上
            filter_complex = (
                f"[0:v]scale={new_width}:{new_height}[scaled];"
                f"[1:v][scaled]overlay={x_offset}:{y_offset}:enable='between(t,0,999999)'"
            )

            cmd = [
                self.ffmpeg_path, "-y",
                "-i", a_video_path,
                "-i", b_looped_file,
                "-filter_complex", filter_complex,
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-pix_fmt", "yuv420p",
                "-an",  # 暂时不要音频
                overlay_file
            ]

            return self.run_ffmpeg_command(cmd, "视频叠加")

        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤3失败: {e}")
            return False

    def step4_stretch_to_9_16(self, overlay_file, stretched_file):
        """步骤4：将处理后的视频变更为9:16，拉伸铺满"""
        try:
            cmd = [
                self.ffmpeg_path, "-y",
                "-i", overlay_file,
                "-vf", "scale=1080:1920:force_original_aspect_ratio=ignore",
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-pix_fmt", "yuv420p",
                "-an",  # 暂时不要音频
                stretched_file
            ]

            return self.run_ffmpeg_command(cmd, "9:16拉伸")

        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤4失败: {e}")
            return False

    def step5_export_tempa(self, stretched_file, audio_file, tempa_file):
        """步骤5：导出为tempa.mp4，1080P，H.264编码，30fps"""
        try:
            cmd = [
                self.ffmpeg_path, "-y",
                "-i", stretched_file,
                "-i", audio_file,
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                "-r", "30",  # 30fps
                "-s", "1080x1920",  # 1080P (9:16)
                "-c:a", "aac",
                "-b:a", "128k",
                "-map", "0:v:0", "-map", "1:a:0",
                tempa_file
            ]

            return self.run_ffmpeg_command(cmd, "导出tempa.mp4")

        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤5失败: {e}")
            return False

    def step6_fix_aspect_ratio(self, tempa_file, output_path):
        """步骤6：修正宽高比 - ffmpeg -i tempa.mp4 -aspect 1:1 output.mp4"""
        try:
            cmd = [
                self.ffmpeg_path, "-y",
                "-i", tempa_file,
                "-aspect", "1:1",
                "-c", "copy",  # 不重新编码，只修改元数据
                output_path
            ]

            return self.run_ffmpeg_command(cmd, "修正宽高比")

        except Exception as e:
            print(f"[蝴蝶翻飞] 步骤6失败: {e}")
            return False
